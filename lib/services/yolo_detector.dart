import 'dart:typed_data';

import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:camera/camera.dart';
import 'package:image/image.dart' as img;
import 'package:tflite_flutter/tflite_flutter.dart';
import 'object_recognition_service.dart';

/// Service de détection d'objets utilisant TensorFlow Lite et YOLO
class YoloDetector {
  static const String _modelPath = 'assets/models/yolov8n.tflite';
  static const String _labelsPath = 'assets/models/coco_labels.txt';

  Interpreter? _interpreter;
  List<String> _labels = [];

  // Paramètres du modèle
  static const int _inputSize = 640;
  static const double _threshold = 0.5;
  static const double _nmsThreshold = 0.5;

  bool _isInitialized = false;
  bool get isInitialized => _isInitialized;

  /// Initialise le détecteur YOLO
  Future<bool> initialize() async {
    try {
      debugPrint('Initialisation du détecteur YOLO...');

      // Charger le modèle TensorFlow Lite
      await _loadModel();

      // Charger les labels
      await _loadLabels();

      _isInitialized = true;
      debugPrint('Détecteur YOLO initialisé avec succès');
      return true;
    } catch (e) {
      debugPrint('Erreur lors de l\'initialisation du détecteur YOLO: $e');
      return false;
    }
  }

  /// Charge le modèle TensorFlow Lite
  Future<void> _loadModel() async {
    try {
      _interpreter = await Interpreter.fromAsset(_modelPath);
      debugPrint('Modèle TensorFlow Lite chargé');
    } catch (e) {
      debugPrint('Erreur lors du chargement du modèle: $e');
      rethrow;
    }
  }

  /// Charge les labels des classes d'objets
  Future<void> _loadLabels() async {
    try {
      final labelsData = await rootBundle.loadString(_labelsPath);
      _labels =
          labelsData.split('\n').where((label) => label.isNotEmpty).toList();
      debugPrint('${_labels.length} labels chargés');
    } catch (e) {
      debugPrint('Erreur lors du chargement des labels: $e');
      // Labels par défaut si le fichier n'est pas trouvé
      _labels = ['person', 'car', 'phone', 'chair', 'table', 'book'];
    }
  }

  /// Détecte les objets dans une image de caméra
  Future<List<DetectedObject>> detectFromCameraImage(
    CameraImage cameraImage,
  ) async {
    if (!_isInitialized || _interpreter == null) return [];

    try {
      // Convertir l'image de la caméra en format utilisable
      final inputImage = _preprocessCameraImage(cameraImage);

      // Effectuer l'inférence
      return await _runInference(inputImage);
    } catch (e) {
      debugPrint('Erreur lors de la détection sur image caméra: $e');
      return [];
    }
  }

  /// Détecte les objets dans une image
  Future<List<DetectedObject>> detectFromImage(Uint8List imageBytes) async {
    if (!_isInitialized || _interpreter == null) return [];

    try {
      // Décoder et préprocesser l'image
      final image = img.decodeImage(imageBytes);
      if (image == null) return [];

      final inputImage = _preprocessImage(image);

      // Effectuer l'inférence
      return await _runInference(inputImage);
    } catch (e) {
      debugPrint('Erreur lors de la détection sur image: $e');
      return [];
    }
  }

  /// Préprocesse une image de caméra
  Float32List _preprocessCameraImage(CameraImage cameraImage) {
    // Convertir YUV420 en RGB
    final rgbImage = _convertYUV420ToRGB(cameraImage);

    // Redimensionner à la taille d'entrée du modèle
    final resized = img.copyResize(
      rgbImage,
      width: _inputSize,
      height: _inputSize,
    );

    // Normaliser les pixels (0-255 -> 0-1)
    final input = Float32List(_inputSize * _inputSize * 3);
    int pixelIndex = 0;

    for (int y = 0; y < _inputSize; y++) {
      for (int x = 0; x < _inputSize; x++) {
        final pixel = resized.getPixel(x, y);
        input[pixelIndex++] = pixel.r / 255.0;
        input[pixelIndex++] = pixel.g / 255.0;
        input[pixelIndex++] = pixel.b / 255.0;
      }
    }

    return input;
  }

  /// Préprocesse une image normale
  Float32List _preprocessImage(img.Image image) {
    // Redimensionner à la taille d'entrée du modèle
    final resized = img.copyResize(
      image,
      width: _inputSize,
      height: _inputSize,
    );

    // Normaliser les pixels
    final input = Float32List(_inputSize * _inputSize * 3);
    int pixelIndex = 0;

    for (int y = 0; y < _inputSize; y++) {
      for (int x = 0; x < _inputSize; x++) {
        final pixel = resized.getPixel(x, y);
        input[pixelIndex++] = pixel.r / 255.0;
        input[pixelIndex++] = pixel.g / 255.0;
        input[pixelIndex++] = pixel.b / 255.0;
      }
    }

    return input;
  }

  /// Convertit une image YUV420 en RGB
  img.Image _convertYUV420ToRGB(CameraImage cameraImage) {
    final width = cameraImage.width;
    final height = cameraImage.height;

    final yPlane = cameraImage.planes[0];
    final uPlane = cameraImage.planes[1];
    final vPlane = cameraImage.planes[2];

    final image = img.Image(width: width, height: height);

    for (int y = 0; y < height; y++) {
      for (int x = 0; x < width; x++) {
        final yIndex = y * yPlane.bytesPerRow + x;
        final uvIndex =
            (y ~/ 2) * uPlane.bytesPerRow + (x ~/ 2) * uPlane.bytesPerPixel!;

        if (yIndex < yPlane.bytes.length && uvIndex < uPlane.bytes.length) {
          final yValue = yPlane.bytes[yIndex];
          final uValue = uPlane.bytes[uvIndex];
          final vValue = vPlane.bytes[uvIndex];

          // Conversion YUV vers RGB
          final r = (yValue + 1.402 * (vValue - 128)).clamp(0, 255).toInt();
          final g =
              (yValue - 0.344136 * (uValue - 128) - 0.714136 * (vValue - 128))
                  .clamp(0, 255)
                  .toInt();
          final b = (yValue + 1.772 * (uValue - 128)).clamp(0, 255).toInt();

          image.setPixel(x, y, img.ColorRgb8(r, g, b));
        }
      }
    }

    return image;
  }

  /// Effectue l'inférence avec le modèle
  Future<List<DetectedObject>> _runInference(Float32List input) async {
    if (_interpreter == null) return [];

    try {
      // Préparer les tenseurs d'entrée et de sortie
      final inputTensor = input.reshape([1, _inputSize, _inputSize, 3]);
      final outputTensor = Float32List(25200 * 85);

      // Exécuter l'inférence
      _interpreter!.run(inputTensor, outputTensor);

      // Post-traiter les résultats
      return _postProcessOutput(outputTensor);
    } catch (e) {
      debugPrint('Erreur lors de l\'inférence: $e');
      return [];
    }
  }

  /// Post-traite la sortie du modèle pour extraire les détections
  List<DetectedObject> _postProcessOutput(Float32List output) {
    final detections = <DetectedObject>[];

    // Parcourir toutes les détections
    for (int i = 0; i < 25200; i++) {
      final offset = i * 85;

      // Extraire les coordonnées et la confiance
      final x = output[offset];
      final y = output[offset + 1];
      final w = output[offset + 2];
      final h = output[offset + 3];
      final confidence = output[offset + 4];

      // Filtrer par seuil de confiance
      if (confidence < _threshold) continue;

      // Trouver la classe avec la plus haute probabilité
      double maxClassScore = 0;
      int maxClassIndex = 0;

      for (int j = 5; j < 85; j++) {
        final classScore = output[offset + j];
        if (classScore > maxClassScore) {
          maxClassScore = classScore;
          maxClassIndex = j - 5;
        }
      }

      final finalConfidence = confidence * maxClassScore;
      if (finalConfidence < _threshold) continue;

      // Convertir les coordonnées (centre -> coin supérieur gauche)
      final x1 = (x - w / 2) / _inputSize;
      final y1 = (y - h / 2) / _inputSize;
      final width = w / _inputSize;
      final height = h / _inputSize;

      // Obtenir le nom de la classe
      final className =
          maxClassIndex < _labels.length ? _labels[maxClassIndex] : 'unknown';

      detections.add(
        DetectedObject(
          className: className,
          confidence: finalConfidence,
          x: x1.clamp(0.0, 1.0),
          y: y1.clamp(0.0, 1.0),
          width: width.clamp(0.0, 1.0),
          height: height.clamp(0.0, 1.0),
        ),
      );
    }

    // Appliquer la suppression non-maximale (NMS)
    return _applyNMS(detections);
  }

  /// Applique la suppression non-maximale pour éliminer les détections redondantes
  List<DetectedObject> _applyNMS(List<DetectedObject> detections) {
    if (detections.isEmpty) return detections;

    // Trier par confiance décroissante
    detections.sort((a, b) => b.confidence.compareTo(a.confidence));

    final result = <DetectedObject>[];
    final suppressed = List<bool>.filled(detections.length, false);

    for (int i = 0; i < detections.length; i++) {
      if (suppressed[i]) continue;

      result.add(detections[i]);

      // Supprimer les détections qui se chevauchent trop
      for (int j = i + 1; j < detections.length; j++) {
        if (suppressed[j]) continue;

        final iou = _calculateIoU(detections[i], detections[j]);
        if (iou > _nmsThreshold) {
          suppressed[j] = true;
        }
      }
    }

    return result;
  }

  /// Calcule l'Intersection over Union (IoU) entre deux détections
  double _calculateIoU(DetectedObject a, DetectedObject b) {
    final x1 = [a.x, b.x].reduce((a, b) => a > b ? a : b);
    final y1 = [a.y, b.y].reduce((a, b) => a > b ? a : b);
    final x2 = [
      (a.x + a.width),
      (b.x + b.width),
    ].reduce((a, b) => a < b ? a : b);
    final y2 = [
      (a.y + a.height),
      (b.y + b.height),
    ].reduce((a, b) => a < b ? a : b);

    if (x2 <= x1 || y2 <= y1) return 0.0;

    final intersection = (x2 - x1) * (y2 - y1);
    final areaA = a.width * a.height;
    final areaB = b.width * b.height;
    final union = areaA + areaB - intersection;

    return intersection / union;
  }

  /// Libère les ressources
  void dispose() {
    _interpreter?.close();
    _interpreter = null;
    _isInitialized = false;
  }
}
