import 'package:flutter/material.dart';
import 'package:speech_to_text/speech_to_text.dart';
import 'package:flutter_tts/flutter_tts.dart';
import 'package:permission_handler/permission_handler.dart';
import 'gemini_chat_service.dart';

/// Service de conversation vocale complète (STT → Gemini → TTS)
class VoiceChatService extends ChangeNotifier {
  // Services
  final SpeechToText _speechToText = SpeechToText();
  final FlutterTts _flutterTts = FlutterTts();
  final GeminiChatService _geminiService = GeminiChatService();
  
  // États
  bool _isInitialized = false;
  bool _isListening = false;
  bool _isSpeaking = false;
  bool _isProcessing = false;
  String _lastRecognizedText = '';
  String _lastError = '';
  
  // Getters
  bool get isInitialized => _isInitialized;
  bool get isListening => _isListening;
  bool get isSpeaking => _isSpeaking;
  bool get isProcessing => _isProcessing;
  bool get isBusy => _isListening || _isSpeaking || _isProcessing;
  String get lastRecognizedText => _lastRecognizedText;
  String get lastError => _lastError;
  GeminiChatService get geminiService => _geminiService;

  /// Initialise tous les services
  Future<bool> initialize({String? geminiApiKey}) async {
    try {
      debugPrint('Initialisation du service de conversation vocale...');
      
      // 1. Demander les permissions
      final micPermission = await Permission.microphone.request();
      if (!micPermission.isGranted) {
        _lastError = 'Permission microphone refusée';
        return false;
      }
      
      // 2. Initialiser Speech-to-Text
      final sttAvailable = await _speechToText.initialize(
        onError: (error) {
          debugPrint('Erreur STT: $error');
          _lastError = 'Erreur STT: ${error.errorMsg}';
          notifyListeners();
        },
        onStatus: (status) {
          debugPrint('Statut STT: $status');
          if (status == 'done' || status == 'notListening') {
            _isListening = false;
            notifyListeners();
          }
        },
      );
      
      if (!sttAvailable) {
        _lastError = 'Speech-to-Text non disponible';
        return false;
      }
      
      // 3. Initialiser Text-to-Speech
      await _initializeTts();
      
      // 4. Initialiser Gemini AI
      if (geminiApiKey != null) {
        _geminiService.setApiKey(geminiApiKey);
      }
      
      final geminiInitialized = await _geminiService.initialize();
      if (!geminiInitialized) {
        _lastError = 'Échec d\'initialisation de Gemini AI';
        return false;
      }
      
      _isInitialized = true;
      _lastError = '';
      
      debugPrint('Service de conversation vocale initialisé avec succès');
      notifyListeners();
      return true;
    } catch (e) {
      _lastError = 'Erreur d\'initialisation: $e';
      debugPrint(_lastError);
      notifyListeners();
      return false;
    }
  }

  /// Initialise le TTS
  Future<void> _initializeTts() async {
    await _flutterTts.setLanguage('fr-FR');
    await _flutterTts.setSpeechRate(0.8);
    await _flutterTts.setVolume(1.0);
    await _flutterTts.setPitch(1.0);
    
    // Callbacks TTS
    _flutterTts.setStartHandler(() {
      _isSpeaking = true;
      notifyListeners();
    });
    
    _flutterTts.setCompletionHandler(() {
      _isSpeaking = false;
      notifyListeners();
    });
    
    _flutterTts.setErrorHandler((msg) {
      debugPrint('Erreur TTS: $msg');
      _isSpeaking = false;
      notifyListeners();
    });
  }

  /// Démarre une conversation vocale complète
  Future<void> startVoiceConversation() async {
    if (!_isInitialized || isBusy) return;
    
    try {
      debugPrint('Démarrage de la conversation vocale...');
      
      // 1. Écouter l'utilisateur (STT)
      await _startListening();
      
    } catch (e) {
      _lastError = 'Erreur lors du démarrage: $e';
      debugPrint(_lastError);
      notifyListeners();
    }
  }

  /// Démarre l'écoute (STT)
  Future<void> _startListening() async {
    if (!_isInitialized || _isListening) return;
    
    try {
      _isListening = true;
      _lastRecognizedText = '';
      _lastError = '';
      notifyListeners();
      
      await _speechToText.listen(
        onResult: (result) async {
          _lastRecognizedText = result.recognizedWords;
          notifyListeners();
          
          // Si la reconnaissance est terminée et qu'on a du texte
          if (result.finalResult && result.recognizedWords.isNotEmpty) {
            _isListening = false;
            notifyListeners();
            
            // Traiter le message avec Gemini
            await _processWithGemini(result.recognizedWords);
          }
        },
        listenFor: const Duration(seconds: 10), // Écoute max 10 secondes
        pauseFor: const Duration(seconds: 3),   // Pause après 3 secondes de silence
        partialResults: true,
        localeId: 'fr_FR',
      );
      
    } catch (e) {
      _lastError = 'Erreur d\'écoute: $e';
      _isListening = false;
      debugPrint(_lastError);
      notifyListeners();
    }
  }

  /// Traite le message avec Gemini AI
  Future<void> _processWithGemini(String userMessage) async {
    if (!_isInitialized || userMessage.isEmpty) return;
    
    try {
      _isProcessing = true;
      notifyListeners();
      
      debugPrint('Traitement avec Gemini: $userMessage');
      
      // Envoyer à Gemini
      final response = await _geminiService.sendMessage(userMessage);
      
      _isProcessing = false;
      notifyListeners();
      
      if (response != null && response.isNotEmpty) {
        // Faire parler la réponse (TTS)
        await _speakResponse(response);
      } else {
        _lastError = 'Pas de réponse de Gemini';
        notifyListeners();
      }
      
    } catch (e) {
      _lastError = 'Erreur de traitement: $e';
      _isProcessing = false;
      debugPrint(_lastError);
      notifyListeners();
    }
  }

  /// Fait parler la réponse (TTS)
  Future<void> _speakResponse(String text) async {
    if (!_isInitialized || text.isEmpty) return;
    
    try {
      debugPrint('Synthèse vocale: $text');
      
      // Nettoyer le texte pour le TTS
      final cleanText = _cleanTextForTts(text);
      
      await _flutterTts.speak(cleanText);
      
    } catch (e) {
      _lastError = 'Erreur de synthèse vocale: $e';
      debugPrint(_lastError);
      notifyListeners();
    }
  }

  /// Nettoie le texte pour le TTS
  String _cleanTextForTts(String text) {
    // Supprimer les caractères spéciaux qui peuvent poser problème
    return text
        .replaceAll(RegExp(r'\*+'), '') // Supprimer les astérisques
        .replaceAll(RegExp(r'#+'), '') // Supprimer les hashtags
        .replaceAll(RegExp(r'`+'), '') // Supprimer les backticks
        .replaceAll(RegExp(r'\s+'), ' ') // Normaliser les espaces
        .trim();
  }

  /// Arrête l'écoute
  Future<void> stopListening() async {
    if (_isListening) {
      await _speechToText.stop();
      _isListening = false;
      notifyListeners();
    }
  }

  /// Arrête la synthèse vocale
  Future<void> stopSpeaking() async {
    if (_isSpeaking) {
      await _flutterTts.stop();
      _isSpeaking = false;
      notifyListeners();
    }
  }

  /// Arrête toutes les activités
  Future<void> stopAll() async {
    await stopListening();
    await stopSpeaking();
    _isProcessing = false;
    notifyListeners();
  }

  /// Envoie un message texte directement (sans STT)
  Future<void> sendTextMessage(String message) async {
    if (!_isInitialized || message.isEmpty || isBusy) return;
    
    await _processWithGemini(message);
  }

  /// Configure la clé API Gemini
  Future<bool> setGeminiApiKey(String apiKey) async {
    _geminiService.setApiKey(apiKey);
    return await _geminiService.initialize();
  }

  /// Efface l'historique des conversations
  void clearHistory() {
    _geminiService.clearHistory();
    _lastRecognizedText = '';
    _lastError = '';
    notifyListeners();
  }

  /// Obtient les statistiques d'utilisation
  Map<String, dynamic> getStats() {
    return {
      'isInitialized': _isInitialized,
      'isListening': _isListening,
      'isSpeaking': _isSpeaking,
      'isProcessing': _isProcessing,
      'lastError': _lastError,
      'geminiStats': _geminiService.getUsageStats(),
    };
  }

  @override
  void dispose() {
    stopAll();
    _speechToText.cancel();
    _flutterTts.stop();
    _geminiService.dispose();
    super.dispose();
  }
}
