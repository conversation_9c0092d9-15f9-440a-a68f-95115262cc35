import 'package:flutter/material.dart';
import 'package:camera/camera.dart';

/// Service de reconnaissance d'objets utilisant YOLO
class ObjectRecognitionService extends ChangeNotifier {
  static final ObjectRecognitionService _instance =
      ObjectRecognitionService._internal();
  factory ObjectRecognitionService() => _instance;
  ObjectRecognitionService._internal();

  // Contrôleur de caméra
  CameraController? _cameraController;
  CameraController? get cameraController => _cameraController;

  // Mode simulé activé
  bool _simulatedMode = true;

  // État du service
  bool _isInitialized = false;
  bool _isDetecting = false;
  bool _isModelLoaded = false;

  // Résultats de détection
  List<DetectedObject> _detectedObjects = [];
  List<DetectedObject> get detectedObjects => _detectedObjects;

  // Getters pour l'état
  bool get isInitialized => _isInitialized;
  bool get isDetecting => _isDetecting;
  bool get isModelLoaded => _isModelLoaded;
  bool get isCameraReady => _cameraController?.value.isInitialized ?? false;

  /// Initialise le service de reconnaissance d'objets
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      // Initialiser la caméra
      await _initializeCamera();

      // Charger le modèle YOLO
      await _loadYoloModel();

      _isInitialized = true;
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint(
        'Erreur lors de l\'initialisation du service de reconnaissance: $e',
      );
      return false;
    }
  }

  /// Initialise la caméra
  Future<void> _initializeCamera() async {
    try {
      final cameras = await availableCameras();
      if (cameras.isEmpty) {
        throw Exception('Aucune caméra disponible');
      }

      // Utiliser la caméra arrière par défaut
      final camera = cameras.firstWhere(
        (camera) => camera.lensDirection == CameraLensDirection.back,
        orElse: () => cameras.first,
      );

      _cameraController = CameraController(
        camera,
        ResolutionPreset.medium,
        enableAudio: false,
        imageFormatGroup: ImageFormatGroup.yuv420,
      );

      await _cameraController!.initialize();
      notifyListeners();
    } catch (e) {
      debugPrint('Erreur lors de l\'initialisation de la caméra: $e');
      rethrow;
    }
  }

  /// Charge le modèle YOLO (version simulée)
  Future<void> _loadYoloModel() async {
    try {
      // Simuler le chargement du modèle
      await Future.delayed(const Duration(seconds: 2));

      _simulatedMode = true;
      _isModelLoaded = true;

      debugPrint('Mode simulé activé pour la reconnaissance d\'objets');
      notifyListeners();
    } catch (e) {
      debugPrint('Erreur lors de l\'initialisation du modèle YOLO: $e');
      // Mode simulé en cas d'erreur complète
      _isModelLoaded = true;
      notifyListeners();
    }
  }

  /// Démarre la détection en temps réel
  Future<void> startDetection() async {
    if (!_isInitialized || _isDetecting || !isCameraReady) return;

    _isDetecting = true;
    notifyListeners();

    try {
      await _cameraController!.startImageStream(_processImage);
    } catch (e) {
      debugPrint('Erreur lors du démarrage de la détection: $e');
      _isDetecting = false;
      notifyListeners();
    }
  }

  /// Arrête la détection
  Future<void> stopDetection() async {
    if (!_isDetecting) return;

    try {
      await _cameraController?.stopImageStream();
      _isDetecting = false;
      _detectedObjects.clear();
      notifyListeners();
    } catch (e) {
      debugPrint('Erreur lors de l\'arrêt de la détection: $e');
    }
  }

  /// Traite une image de la caméra
  void _processImage(CameraImage cameraImage) async {
    if (!_isModelLoaded) return;

    try {
      // Mode simulé pour les tests (remplacer par YOLO réel plus tard)
      _simulateDetection();
      notifyListeners();
    } catch (e) {
      debugPrint('Erreur lors du traitement de l\'image: $e');
      // En cas d'erreur, utiliser le mode simulé
      _simulateDetection();
      notifyListeners();
    }
  }

  /// Simule une détection d'objets pour les tests
  void _simulateDetection() {
    final now = DateTime.now().millisecondsSinceEpoch;
    final random = (now ~/ 1000) % 100; // Change toutes les secondes

    // Liste d'objets possibles à détecter
    final possibleObjects = [
      {'class': 'person', 'confidence': 0.85 + (random % 10) * 0.01},
      {'class': 'phone', 'confidence': 0.72 + (random % 15) * 0.01},
      {'class': 'chair', 'confidence': 0.91 + (random % 8) * 0.01},
      {'class': 'table', 'confidence': 0.88 + (random % 10) * 0.01},
      {'class': 'book', 'confidence': 0.76 + (random % 12) * 0.01},
      {'class': 'bottle', 'confidence': 0.82 + (random % 15) * 0.01},
      {'class': 'cup', 'confidence': 0.79 + (random % 18) * 0.01},
      {'class': 'laptop', 'confidence': 0.93 + (random % 6) * 0.01},
      {'class': 'car', 'confidence': 0.89 + (random % 8) * 0.01},
      {'class': 'dog', 'confidence': 0.87 + (random % 10) * 0.01},
      {'class': 'cat', 'confidence': 0.84 + (random % 12) * 0.01},
      {'class': 'tv', 'confidence': 0.92 + (random % 7) * 0.01},
      {'class': 'clock', 'confidence': 0.78 + (random % 15) * 0.01},
      {'class': 'keyboard', 'confidence': 0.81 + (random % 14) * 0.01},
      {'class': 'mouse', 'confidence': 0.75 + (random % 20) * 0.01},
    ];

    // Simuler une détection toutes les 2-3 secondes
    if (random % 30 < 8) {
      // ~25% de chance de détecter quelque chose
      final numObjects = (random % 3) + 1; // 1 à 3 objets
      _detectedObjects = [];

      for (int i = 0; i < numObjects && i < possibleObjects.length; i++) {
        final objIndex = (random + i * 17) % possibleObjects.length;
        final objData = possibleObjects[objIndex];

        // Générer des positions aléatoires mais réalistes
        final x = 0.1 + ((random + i * 23) % 60) * 0.01; // 0.1 à 0.7
        final y = 0.1 + ((random + i * 31) % 60) * 0.01; // 0.1 à 0.7
        final width = 0.15 + ((random + i * 13) % 25) * 0.01; // 0.15 à 0.4
        final height = 0.15 + ((random + i * 19) % 35) * 0.01; // 0.15 à 0.5

        _detectedObjects.add(
          DetectedObject(
            className: objData['class'] as String,
            confidence: (objData['confidence'] as double).clamp(0.0, 1.0),
            x: x,
            y: y,
            width: width,
            height: height,
          ),
        );
      }
    } else {
      _detectedObjects = [];
    }
  }

  /// Prend une photo et effectue la détection
  Future<List<DetectedObject>> detectFromPhoto() async {
    if (!isCameraReady || !_isModelLoaded) return [];

    try {
      await _cameraController!.takePicture();

      // Mode simulé - retourner des objets détectés variés
      await Future.delayed(const Duration(milliseconds: 500));

      // Générer des objets aléatoires basés sur l'heure actuelle
      final now = DateTime.now().millisecondsSinceEpoch;
      final random = (now ~/ 100) % 100;

      final possibleObjects = [
        {'class': 'person', 'confidence': 0.85 + (random % 15) * 0.01},
        {'class': 'phone', 'confidence': 0.72 + (random % 20) * 0.01},
        {'class': 'chair', 'confidence': 0.91 + (random % 8) * 0.01},
        {'class': 'table', 'confidence': 0.88 + (random % 10) * 0.01},
        {'class': 'book', 'confidence': 0.76 + (random % 15) * 0.01},
        {'class': 'bottle', 'confidence': 0.82 + (random % 12) * 0.01},
        {'class': 'cup', 'confidence': 0.79 + (random % 18) * 0.01},
        {'class': 'laptop', 'confidence': 0.93 + (random % 6) * 0.01},
        {'class': 'car', 'confidence': 0.89 + (random % 8) * 0.01},
        {'class': 'dog', 'confidence': 0.87 + (random % 10) * 0.01},
        {'class': 'cat', 'confidence': 0.84 + (random % 12) * 0.01},
        {'class': 'tv', 'confidence': 0.92 + (random % 7) * 0.01},
        {'class': 'clock', 'confidence': 0.78 + (random % 15) * 0.01},
        {'class': 'keyboard', 'confidence': 0.81 + (random % 14) * 0.01},
        {'class': 'mouse', 'confidence': 0.75 + (random % 20) * 0.01},
        {'class': 'apple', 'confidence': 0.73 + (random % 22) * 0.01},
        {'class': 'banana', 'confidence': 0.77 + (random % 18) * 0.01},
        {'class': 'orange', 'confidence': 0.74 + (random % 21) * 0.01},
      ];

      // Sélectionner 1 à 3 objets aléatoirement
      final numObjects = (random % 3) + 1;
      final detectedObjects = <DetectedObject>[];

      for (int i = 0; i < numObjects; i++) {
        final objIndex = (random + i * 13) % possibleObjects.length;
        final objData = possibleObjects[objIndex];

        // Générer des positions aléatoires
        final x = 0.05 + ((random + i * 17) % 70) * 0.01;
        final y = 0.05 + ((random + i * 23) % 70) * 0.01;
        final width = 0.1 + ((random + i * 11) % 30) * 0.01;
        final height = 0.1 + ((random + i * 19) % 40) * 0.01;

        detectedObjects.add(
          DetectedObject(
            className: objData['class'] as String,
            confidence: (objData['confidence'] as double).clamp(0.0, 1.0),
            x: x,
            y: y,
            width: width,
            height: height,
          ),
        );
      }

      return detectedObjects;
    } catch (e) {
      debugPrint('Erreur lors de la détection sur photo: $e');
      return [];
    }
  }

  /// Libère les ressources
  @override
  void dispose() {
    stopDetection();
    _cameraController?.dispose();
    super.dispose();
  }
}

/// Classe représentant un objet détecté
class DetectedObject {
  final String className;
  final double confidence;
  final double x;
  final double y;
  final double width;
  final double height;

  DetectedObject({
    required this.className,
    required this.confidence,
    required this.x,
    required this.y,
    required this.width,
    required this.height,
  });

  factory DetectedObject.fromMap(Map<String, dynamic> map) {
    return DetectedObject(
      className: map['class'] ?? 'Inconnu',
      confidence: (map['confidence'] ?? 0.0).toDouble(),
      x: (map['x'] ?? 0.0).toDouble(),
      y: (map['y'] ?? 0.0).toDouble(),
      width: (map['width'] ?? 0.0).toDouble(),
      height: (map['height'] ?? 0.0).toDouble(),
    );
  }

  /// Retourne le pourcentage de confiance formaté
  String get confidencePercentage =>
      '${(confidence * 100).toStringAsFixed(1)}%';

  /// Retourne le nom de classe traduit en français
  String get classNameFr {
    const translations = {
      'person': 'Personne',
      'car': 'Voiture',
      'dog': 'Chien',
      'cat': 'Chat',
      'chair': 'Chaise',
      'table': 'Table',
      'phone': 'Téléphone',
      'laptop': 'Ordinateur portable',
      'book': 'Livre',
      'bottle': 'Bouteille',
      'cup': 'Tasse',
      'knife': 'Couteau',
      'spoon': 'Cuillère',
      'fork': 'Fourchette',
      'bowl': 'Bol',
      'banana': 'Banane',
      'apple': 'Pomme',
      'orange': 'Orange',
      'clock': 'Horloge',
      'tv': 'Télévision',
      'keyboard': 'Clavier',
      'mouse': 'Souris',
    };

    return translations[className.toLowerCase()] ?? className;
  }
}
