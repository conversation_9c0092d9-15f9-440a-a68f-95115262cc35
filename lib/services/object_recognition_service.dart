import 'package:flutter/material.dart';
import 'package:camera/camera.dart';

/// Service de reconnaissance d'objets utilisant YOLO
class ObjectRecognitionService extends ChangeNotifier {
  static final ObjectRecognitionService _instance = ObjectRecognitionService._internal();
  factory ObjectRecognitionService() => _instance;
  ObjectRecognitionService._internal();

  // Contrôleur de caméra
  CameraController? _cameraController;
  CameraController? get cameraController => _cameraController;

  // Mode simulé activé
  bool _simulatedMode = true;

  // État du service
  bool _isInitialized = false;
  bool _isDetecting = false;
  bool _isModelLoaded = false;

  // Résultats de détection
  List<DetectedObject> _detectedObjects = [];
  List<DetectedObject> get detectedObjects => _detectedObjects;

  // Getters pour l'état
  bool get isInitialized => _isInitialized;
  bool get isDetecting => _isDetecting;
  bool get isModelLoaded => _isModelLoaded;
  bool get isCameraReady => _cameraController?.value.isInitialized ?? false;

  /// Initialise le service de reconnaissance d'objets
  Future<bool> initialize() async {
    if (_isInitialized) return true;

    try {
      // Initialiser la caméra
      await _initializeCamera();

      // Charger le modèle YOLO
      await _loadYoloModel();

      _isInitialized = true;
      notifyListeners();
      return true;
    } catch (e) {
      debugPrint('Erreur lors de l\'initialisation du service de reconnaissance: $e');
      return false;
    }
  }

  /// Initialise la caméra
  Future<void> _initializeCamera() async {
    try {
      final cameras = await availableCameras();
      if (cameras.isEmpty) {
        throw Exception('Aucune caméra disponible');
      }

      // Utiliser la caméra arrière par défaut
      final camera = cameras.firstWhere(
        (camera) => camera.lensDirection == CameraLensDirection.back,
        orElse: () => cameras.first,
      );

      _cameraController = CameraController(
        camera,
        ResolutionPreset.medium,
        enableAudio: false,
        imageFormatGroup: ImageFormatGroup.yuv420,
      );

      await _cameraController!.initialize();
      notifyListeners();
    } catch (e) {
      debugPrint('Erreur lors de l\'initialisation de la caméra: $e');
      rethrow;
    }
  }

  /// Charge le modèle YOLO (version simulée)
  Future<void> _loadYoloModel() async {
    try {
      // Simuler le chargement du modèle
      await Future.delayed(const Duration(seconds: 2));

      _simulatedMode = true;
      _isModelLoaded = true;

      debugPrint('Mode simulé activé pour la reconnaissance d\'objets');
      notifyListeners();
    } catch (e) {
      debugPrint('Erreur lors de l\'initialisation du modèle YOLO: $e');
      // Mode simulé en cas d'erreur complète
      _isModelLoaded = true;
      notifyListeners();
    }
  }

  /// Démarre la détection en temps réel
  Future<void> startDetection() async {
    if (!_isInitialized || _isDetecting || !isCameraReady) return;

    _isDetecting = true;
    notifyListeners();

    try {
      await _cameraController!.startImageStream(_processImage);
    } catch (e) {
      debugPrint('Erreur lors du démarrage de la détection: $e');
      _isDetecting = false;
      notifyListeners();
    }
  }

  /// Arrête la détection
  Future<void> stopDetection() async {
    if (!_isDetecting) return;

    try {
      await _cameraController?.stopImageStream();
      _isDetecting = false;
      _detectedObjects.clear();
      notifyListeners();
    } catch (e) {
      debugPrint('Erreur lors de l\'arrêt de la détection: $e');
    }
  }

  /// Traite une image de la caméra
  void _processImage(CameraImage cameraImage) async {
    if (!_isModelLoaded) return;

    try {
      // Mode simulé pour les tests (remplacer par YOLO réel plus tard)
      _simulateDetection();
      notifyListeners();
    } catch (e) {
      debugPrint('Erreur lors du traitement de l\'image: $e');
      // En cas d'erreur, utiliser le mode simulé
      _simulateDetection();
      notifyListeners();
    }
  }

  /// Simule une détection d'objets pour les tests
  void _simulateDetection() {
    // Simuler quelques objets détectés de temps en temps
    if (DateTime.now().millisecondsSinceEpoch % 3000 < 100) {
      _detectedObjects = [
        DetectedObject(
          className: 'person',
          confidence: 0.85,
          x: 0.2,
          y: 0.3,
          width: 0.3,
          height: 0.4,
        ),
        DetectedObject(
          className: 'phone',
          confidence: 0.72,
          x: 0.6,
          y: 0.1,
          width: 0.15,
          height: 0.25,
        ),
      ];
    } else if (DateTime.now().millisecondsSinceEpoch % 5000 < 100) {
      _detectedObjects = [
        DetectedObject(
          className: 'chair',
          confidence: 0.91,
          x: 0.1,
          y: 0.4,
          width: 0.4,
          height: 0.5,
        ),
      ];
    } else {
      _detectedObjects = [];
    }
  }



  /// Prend une photo et effectue la détection
  Future<List<DetectedObject>> detectFromPhoto() async {
    if (!isCameraReady || !_isModelLoaded) return [];

    try {
      await _cameraController!.takePicture();

      // Mode simulé - retourner quelques objets détectés
      await Future.delayed(const Duration(milliseconds: 500));

      return [
        DetectedObject(
          className: 'person',
          confidence: 0.92,
          x: 0.1,
          y: 0.2,
          width: 0.4,
          height: 0.6,
        ),
        DetectedObject(
          className: 'phone',
          confidence: 0.78,
          x: 0.6,
          y: 0.1,
          width: 0.2,
          height: 0.3,
        ),
      ];
    } catch (e) {
      debugPrint('Erreur lors de la détection sur photo: $e');
      return [];
    }
  }

  /// Libère les ressources
  @override
  void dispose() {
    stopDetection();
    _cameraController?.dispose();
    super.dispose();
  }
}

/// Classe représentant un objet détecté
class DetectedObject {
  final String className;
  final double confidence;
  final double x;
  final double y;
  final double width;
  final double height;

  DetectedObject({
    required this.className,
    required this.confidence,
    required this.x,
    required this.y,
    required this.width,
    required this.height,
  });

  factory DetectedObject.fromMap(Map<String, dynamic> map) {
    return DetectedObject(
      className: map['class'] ?? 'Inconnu',
      confidence: (map['confidence'] ?? 0.0).toDouble(),
      x: (map['x'] ?? 0.0).toDouble(),
      y: (map['y'] ?? 0.0).toDouble(),
      width: (map['width'] ?? 0.0).toDouble(),
      height: (map['height'] ?? 0.0).toDouble(),
    );
  }

  /// Retourne le pourcentage de confiance formaté
  String get confidencePercentage => '${(confidence * 100).toStringAsFixed(1)}%';

  /// Retourne le nom de classe traduit en français
  String get classNameFr {
    const translations = {
      'person': 'Personne',
      'car': 'Voiture',
      'dog': 'Chien',
      'cat': 'Chat',
      'chair': 'Chaise',
      'table': 'Table',
      'phone': 'Téléphone',
      'laptop': 'Ordinateur portable',
      'book': 'Livre',
      'bottle': 'Bouteille',
      'cup': 'Tasse',
      'knife': 'Couteau',
      'spoon': 'Cuillère',
      'fork': 'Fourchette',
      'bowl': 'Bol',
      'banana': 'Banane',
      'apple': 'Pomme',
      'orange': 'Orange',
      'clock': 'Horloge',
      'tv': 'Télévision',
      'keyboard': 'Clavier',
      'mouse': 'Souris',
    };

    return translations[className.toLowerCase()] ?? className;
  }
}
